'use client';

import { useState, useRef } from 'react';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Loading from '@/components/dentoui/Loading';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AddPatientModal from '@/components/hastalar/AddPatientModal';
import ToothSelectionModal from '@/components/istem-formu/ToothSelectionModal';
import BitewingSideModal from '@/components/istem-formu/BitewingSideModal';
import {
  User,
  Save,
  X,
  Search,
  Plus,
  Camera,
  UserPlus
} from 'lucide-react';
import DentoButtonSecondary from '@/components/dentoui/DentoButtonSecondary';
import DentoButtonPrimary from '@/components/dentoui/DentoButtonPrimary';
import Header from '@/components/dentoui/Header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { IstemFormuData } from '@/lib/types';
import { saveIstemFormu } from '@/lib/services/istemFormuService';
import { toast } from 'sonner';
import { Patient } from '@/lib/types';
import { getPatientsByDoctorId, addPatient } from '@/lib/services/patientService';
import { useEffect } from 'react';

interface PatientFormData {
  id?: string;
  firstName: string;
  lastName: string;
  tcKimlik: string;
  birthDate: Date | undefined;
  gender: string;
  phone: string;
  email?: string;
}

export default function CreateIstemFormu() {
  const router = useRouter();
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showPatientSearch, setShowPatientSearch] = useState(false);
  const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
  const [isToothSelectionModalOpen, setIsToothSelectionModalOpen] = useState(false);
  const [isBitewingSideModalOpen, setIsBitewingSideModalOpen] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // useAuth handles redirecting unauthenticated users to login when options are provided
  const { user, loading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  useEffect(() => {
    if (user) {
      const fetchPatients = async () => {
        try {
          const fetchedPatients = await getPatientsByDoctorId(user.uid);
          setPatients(fetchedPatients);
        } catch (error) {
          console.error("Failed to fetch patients:", error);
          toast.error("Hastalar yüklenirken bir hata oluştu.");
        }
      };

      fetchPatients();
    }
  }, [user]);

  // Handle clicking outside to close search dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setShowPatientSearch(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Form state
  const [formData, setFormData] = useState<Partial<IstemFormuData>>({
    patientId: '',
    diagnosis: '',
    notes: '',
    images: [],
    xrayTypes: [] as string[],
    bitewingSides: [] as string[],
    paymentResponsible: 'clinic',
    priorityStatus: 'Normal'
  });

  if (loading) {
    return <Loading message="İstem formu yükleniyor..." />;
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePriorityChange = (value: string) => {
    handleInputChange('priorityStatus', value);
  };

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
    setFormData(prev => ({ ...prev, patientId: patient.id }));
    setShowPatientSearch(false);
    setSearchQuery(''); // Clear search after selection
  };

  const handleClearSelectedPatient = () => {
    setSelectedPatient(null);
    setFormData(prev => ({ ...prev, patientId: '' }));
  };

  const filteredPatients = patients.filter(patient =>
    `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
    patient.tcKimlik.includes(searchQuery)
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error('Bu işlemi yapmak için giriş yapmalısınız.');
      return;
    }
    if (!selectedPatient) {
      toast.error('Lütfen bir hasta seçin.');
      return;
    }

    try {
      const formToSave: Omit<IstemFormuData, 'id' | 'createdAt' | 'status'> = {
        patientId: selectedPatient.id,
        userId: user.uid,
        diagnosis: formData.diagnosis || '',
        notes: formData.notes || '',
        images: formData.images || [],
        xrayTypes: formData.xrayTypes || [],
        bitewingSides: formData.bitewingSides || [],
        selectedTeeth: formData.selectedTeeth || '',
        paymentResponsible: formData.paymentResponsible || 'clinic',
        priorityStatus: formData.priorityStatus || 'Normal',
      };
      
      await saveIstemFormu(formToSave, user.uid);
      toast.success('İstem formu başarıyla kaydedildi.');
      router.push('/dashboard/istem-formu');
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Formu kaydederken bir hata oluştu.');
    }
  };

  const handleSaveDraft = () => {
    console.log('Draft saved:', formData);
    // Handle draft saving
  };

  const handleAddPatient = async (patientData: PatientFormData) => {
    if (!user) {
      toast.error("Yeni hasta eklemek için giriş yapmalısınız.");
      return;
    }
  
    try {
      const patientToAdd: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'> = {
        doctorId: user.uid,
        firstName: patientData.firstName,
        lastName: patientData.lastName,
        tcKimlik: patientData.tcKimlik,
        birthDate: patientData.birthDate || new Date(),
        gender: patientData.gender,
        phone: patientData.phone,
        email: patientData.email,
      };
  
      const newPatientId = await addPatient(patientToAdd);
      
      const newPatient: Patient = {
        ...patientToAdd,
        id: newPatientId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
  
      setPatients(prev => [...prev, newPatient]);
      handlePatientSelect(newPatient);
      setIsAddPatientModalOpen(false);
      toast.success("Yeni hasta başarıyla eklendi ve seçildi.");
  
    } catch (error) {
      console.error("Error adding patient:", error);
      toast.error("Hasta eklenirken bir hata oluştu.");
    }
  };

  const handleXrayToggle = (xrayType: string) => {
    const currentXrayTypes = formData.xrayTypes || [];
    if (xrayType === 'periapikal') {
      if (currentXrayTypes.includes('periapikal')) {
        // Deselect periapikal
        setFormData(prev => ({
          ...prev,
          xrayTypes: (prev.xrayTypes || []).filter(type => type !== 'periapikal'),
          selectedTeeth: '' // Optionally clear selected teeth
        }));
      } else {
        setIsToothSelectionModalOpen(true);
      }
    } else if (xrayType === 'bitewing') {
      if (currentXrayTypes.includes('bitewing')) {
        // Deselect bitewing and clear sides
        setFormData(prev => ({
          ...prev,
          xrayTypes: (prev.xrayTypes || []).filter(type => type !== 'bitewing'),
          bitewingSides: []
        }));
      } else {
        setIsBitewingSideModalOpen(true);
      }
    } else {
      setFormData(prev => ({
        ...prev,
        xrayTypes: currentXrayTypes.includes(xrayType)
          ? (prev.xrayTypes || []).filter(type => type !== xrayType)
          : [...(prev.xrayTypes || []), xrayType]
      }));
    }
  };

  const handleSelectBitewingSides = (sides: string[]) => {
    setFormData(prev => ({ ...prev, bitewingSides: sides }));
    setIsBitewingSideModalOpen(false);
    // Also add 'bitewing' to xrayTypes if sides are selected
    if (sides.length > 0 && !(formData.xrayTypes || []).includes('bitewing')) {
      setFormData(prev => ({
        ...prev,
        xrayTypes: [...(prev.xrayTypes || []), 'bitewing']
      }));
    }
  };

  const handleSelectTooth = (teeth: string[]) => {
    setFormData(prev => ({ ...prev, selectedTeeth: teeth.join(', ') }));
    setIsToothSelectionModalOpen(false);
    // Also add 'periapikal' to xrayTypes if a tooth is selected
    if (teeth.length > 0 && !(formData.xrayTypes || []).includes('periapikal')) {
      setFormData(prev => ({
        ...prev,
        xrayTypes: [...(prev.xrayTypes || []), 'periapikal']
      }));
    }
  };

  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard' },
    { label: 'İstem Formu', href: '/dashboard/istem-formu' },
    { label: 'Yeni Form', href: '/dashboard/istem-formu/create', isActive: true },
  ];

  const headerActions = (
    <>
      <DentoButtonSecondary
        onClick={handleSaveDraft}
        icon={<Save className="w-5 h-5" />}
        iconAnimation="group-hover:scale-110"
        bgColor="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700"
        textColor="text-white"
        className="!px-6 !py-3 !font-semibold !border-0 !backdrop-blur-none"
      >
        Taslak Kaydet
      </DentoButtonSecondary>
      <DentoButtonSecondary
        onClick={() => router.back()}
        icon={<X className="w-5 h-5" />}
        iconAnimation="group-hover:rotate-90"
        bgColor="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
        textColor="text-white"
        className="!px-6 !py-3 !font-semibold !border-0 !backdrop-blur-none"
      >
        İptal
      </DentoButtonSecondary>
    </>
  );

  const xrayOptions = [
    {
      id: 'periapikal',
      title: 'Periapikal Röntgen',
      subtitle: 'Tek diş görüntüleme',
      englishTitle: 'Periapical X-Ray',
      englishSubtitle: 'Single tooth imaging'
    },
    {
      id: 'panoramik',
      title: 'Panoramik Röntgen',
      subtitle: 'Tüm ağız görüntüleme',
      englishTitle: 'Panoramic X-Ray',
      englishSubtitle: 'Full mouth imaging'
    },
    {
      id: 'bitewing',
      title: 'Bitewing Röntgen',
      subtitle: 'Yan diş görüntüleme',
      englishTitle: 'Bitewing X-Ray',
      englishSubtitle: 'Side teeth imaging'
    },
    {
      id: 'cbct',
      title: 'CBCT (3D Tomografi)',
      subtitle: '3 boyutlu görüntüleme',
      englishTitle: 'CBCT (3D Tomography)',
      englishSubtitle: '3-dimensional imaging'
    },
    {
      id: 'sefalometrik',
      title: 'Sefalometrik',
      subtitle: 'Yan kafa röntgeni',
      englishTitle: 'Cephalometric',
      englishSubtitle: 'Side head x-ray'
    },
    {
      id: 'tmj',
      title: 'TMJ Görüntüleme',
      subtitle: 'Çene eklemi',
      englishTitle: 'TMJ Imaging',
      englishSubtitle: 'Jaw joint'
    }
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="Yeni İstem Formu"
          description="Yeni dental işlem kaydı oluşturun"
          breadcrumbs={breadcrumbs}
          rightComponent={headerActions}
        />

        {/* Form Content */}
        <div className="p-8 max-w-6xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Patient Selection & Basic Info */}
            <div className="bg-gradient-to-br from-white via-white to-blue-50/30 rounded-2xl shadow-xl border border-gray-200/50 p-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <User className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">Hasta ve Temel Bilgiler</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Patient Selection */}
                <div className="relative">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Hasta Seçimi *</label>

                  {/* Selected Patient Tag */}
                  {selectedPatient && (
                    <div className="mb-3 flex items-center gap-2">
                      <div className="inline-flex items-center gap-2 px-3 py-2 bg-green-100 text-green-800 rounded-lg border border-green-200">
                        <User className="w-4 h-4" />
                        <span className="font-medium">{`${selectedPatient.firstName} ${selectedPatient.lastName}`}</span>
                        <button
                          type="button"
                          onClick={handleClearSelectedPatient}
                          className="ml-1 hover:bg-green-200 rounded-full p-1 transition-colors"
                          title="Hasta seçimini temizle"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  )}

                  <div className="relative" ref={searchContainerRef}>
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                        setShowPatientSearch(true);
                      }}
                      onFocus={() => setShowPatientSearch(true)}
                      placeholder="Hasta adı veya TC kimlik ile ara..."
                      className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
                    />
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />

                    {showPatientSearch && searchQuery && (
                      <div className="absolute z-10 top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-60 overflow-y-auto">
                        {filteredPatients.map((patient) => (
                          <button
                            key={patient.id}
                            type="button"
                            onClick={() => handlePatientSelect(patient)}
                            className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                          >
                            <div className="font-semibold text-gray-900">{`${patient.firstName} ${patient.lastName}`}</div>
                            <div className="text-sm text-gray-500">TC: {patient.tcKimlik}</div>
                          </button>
                        ))}
                        {filteredPatients.length === 0 && (
                          <div className="px-4 py-3 text-gray-500 text-center">Hasta bulunamadı</div>
                        )}
                        <div className="border-t border-gray-200 p-2">
                          <button
                            type="button"
                            onClick={() => setIsAddPatientModalOpen(true)}
                            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105"
                          >
                            <Plus className="w-4 h-4" />
                            <span className="font-medium">Yeni Hasta Ekle</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* New Patient Button */}
                <div className="flex items-end">
                  <DentoButtonPrimary
                    onClick={() => setIsAddPatientModalOpen(true)}
                    icon={<UserPlus className="w-5 h-5" />}
                    className="!py-4"
                  >
                    Yeni Hasta Ekle
                  </DentoButtonPrimary>
                </div>
              </div>
            </div>

            {/* X-Ray Imaging Selection */}
            <div className="bg-gradient-to-br from-white via-white to-orange-50/30 rounded-2xl shadow-xl border border-gray-200/50 p-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Camera className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">Görüntüleme Seçimi</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {xrayOptions.map((option) => (
                  <div
                    key={option.id}
                    onClick={() => handleXrayToggle(option.id)}
                    className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
                      (formData.xrayTypes || []).includes(option.id)
                        ? 'border-orange-500 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg'
                        : 'border-gray-200 bg-white hover:border-orange-300'
                    }`}
                  >
                    {/* Checkbox indicator */}
                    <div className={`absolute top-4 right-4 w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
                      (formData.xrayTypes || []).includes(option.id)
                        ? 'border-orange-500 bg-orange-500'
                        : 'border-gray-300'
                    }`}>
                      {(formData.xrayTypes || []).includes(option.id) && (
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>

                    {/* Content */}
                    <div className="pr-8">
                      <h3 className="text-lg font-bold text-gray-900 mb-1">{option.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{option.subtitle}</p>
                      <div className="text-xs text-gray-500">
                        <p className="font-medium">{option.englishTitle}</p>
                        <p>{option.englishSubtitle}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {(formData.xrayTypes || []).length > 0 && (
                <>
                  <div className="mt-6 p-4 bg-orange-50 rounded-xl border border-orange-200">
                    <h4 className="text-sm font-semibold text-orange-800 mb-2">Seçilen Görüntüleme Türleri:</h4>
                    <div className="flex flex-wrap gap-2">
                      {(formData.xrayTypes || []).map((typeId) => {
                        const option = xrayOptions.find(opt => opt.id === typeId);
                        return option ? (
                          <span
                            key={typeId}
                            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                          >
                            {option.title}
                            {typeId === 'bitewing' && (formData.bitewingSides || []).length > 0 && (
                              <span className="ml-1 text-orange-600">
                                ({(formData.bitewingSides || []).map(side => 
                                  side === 'left' ? 'Sol' : 'Sağ'
                                ).join(', ')})
                              </span>
                            )}
                            {typeId === 'periapikal' && formData.selectedTeeth && (
                              <span className="ml-1 text-orange-600">
                                (Diş: {formData.selectedTeeth.split(', ').map(toothId => 
                                  toothId.replace('tooth-', '')
                                ).join(', ')})
                              </span>
                            )}
                          </span>
                        ) : null;
                      })}
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Additional Information */}
            <div className="bg-gradient-to-br from-white via-white to-emerald-50/30 rounded-2xl shadow-xl border border-gray-200/50 p-8">
              <div className="flex items-center space-x-4 mb-8">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Plus className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">Ek Bilgiler</h2>
              </div>

              {/* Payment and Priority Section - Always Visible */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 p-6 bg-gradient-to-br from-emerald-50 to-emerald-100/50 rounded-xl border border-emerald-200/60">
                {/* Payment Responsibility */}
                <div>
                  <label className="block text-sm font-semibold text-emerald-800 mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Ödeme Sorumlusu *
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center cursor-pointer p-3 rounded-lg bg-white/70 hover:bg-white transition-colors border border-emerald-200/50">
                      <input
                        type="radio"
                        name="paymentResponsible"
                        value="clinic"
                        checked={(formData.paymentResponsible || 'clinic') === 'clinic'}
                        onChange={(e) => handleInputChange('paymentResponsible', e.target.value)}
                        className="w-4 h-4 text-emerald-600 border-emerald-300 focus:ring-emerald-500"
                      />
                      <span className="ml-3 text-sm font-medium text-emerald-800">Kliniğimiz tarafından ödenecek</span>
                    </label>
                    <label className="flex items-center cursor-pointer p-3 rounded-lg bg-white/70 hover:bg-white transition-colors border border-emerald-200/50">
                      <input
                        type="radio"
                        name="paymentResponsible"
                        value="patient"
                        checked={(formData.paymentResponsible || 'clinic') === 'patient'}
                        onChange={(e) => handleInputChange('paymentResponsible', e.target.value)}
                        className="w-4 h-4 text-emerald-600 border-emerald-300 focus:ring-emerald-500"
                      />
                      <span className="ml-3 text-sm font-medium text-emerald-800">Hasta tarafından ödenecek</span>
                    </label>
                  </div>
                </div>

                {/* Priority Status */}
                <div>
                  <label className="block text-sm font-semibold text-emerald-800 mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Öncelik Durumu
                  </label>
                  <Select
                    value={formData.priorityStatus}
                    onValueChange={handlePriorityChange}
                  >
                    <SelectTrigger className="w-full px-4 py-5 border border-emerald-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent bg-white/90 shadow-sm text-emerald-800 font-medium">
                      <SelectValue placeholder="Öncelik durumu seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Normal">Normal</SelectItem>
                      <SelectItem value="Acil">Acil</SelectItem>
                      <SelectItem value="Çok Acil">Çok Acil</SelectItem>
                      <SelectItem value="Düşük">Düşük</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Diagnosis */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-3">Teşhis *</label>
                <textarea
                  value={formData.diagnosis}
                  onChange={(e) => handleInputChange('diagnosis', e.target.value)}
                  placeholder="Hastanın durumu ve teşhis bilgileri..."
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent bg-white shadow-sm resize-none"
                  required
                />
              </div>

              {/* Notes */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-3">Notlar</label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Ek notlar ve özel durumlar..."
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent bg-white shadow-sm resize-none"
                />
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Görüntüler</label>
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-emerald-400 transition-colors">
                  <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">Fotoğraf veya röntgen yüklemek için tıklayın</p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="inline-flex items-center px-4 py-2 bg-emerald-500 hover:bg-emerald-600 text-white rounded-lg cursor-pointer transition-colors"
                  >
                    Dosya Seç
                  </label>
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-8 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-xl transition-colors font-semibold"
              >
                İptal
              </button>
              <button
                type="submit"
                className="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                İşlemi Kaydet
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Add Patient Modal */}
      <AddPatientModal
        isOpen={isAddPatientModalOpen}
        onClose={() => setIsAddPatientModalOpen(false)}
        onSubmit={handleAddPatient}
      />

      <ToothSelectionModal
        isOpen={isToothSelectionModalOpen}
        onClose={() => setIsToothSelectionModalOpen(false)}
        onSelectTooth={handleSelectTooth}
      />

      <BitewingSideModal
        isOpen={isBitewingSideModalOpen}
        onClose={() => setIsBitewingSideModalOpen(false)}
        onSelectSides={handleSelectBitewingSides}
      />
    </div>
  );
} 